<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv" >
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="header-content" :class="{ 'create-img': isCreateImg }">
        <div class="header">
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRule = true"><div>活动规则</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showMyPrize = true"><div>我的奖品</div></div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="ShowToast"><div>进店逛逛</div></div>
        </div>
      </div>
    </div>
    <div class="twoDivClass" :style="furnishStyles.blindBoxBg.value">
      <div class="prizeClass">
        <div class="prizeItemClass" v-for="(item, index) in prizeListAll" :key="index">
          <div v-if="!item.isDraw">
              <img class="boxPrizes" v-if="item.isSelect" :src="item.lightLogoImg" alt=""/>
              <img class="boxPrizes" v-else :src="item.logoImg" @click.stop="selectMhClick(item, index)" alt=""/>
          </div>
          <div v-else>
              <img class="boxPrizes" :src="item.isDrawLogoImg" alt="" />
          </div>
        </div>
      </div>
    </div>
    <div class="numText">
      剩余拆盲盒次数<span>0</span>次
    </div>
  </div>
  <div v-if="!isCreateImg">
    <!-- 规则弹窗 -->
    <VanPopup teleport="body" v-model:show="showRule" position="bottom">
      <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
    </VanPopup>
    <!-- 我的奖品弹窗 -->
    <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
      <MyPrize @close="showMyPrize = false"></MyPrize>
    </VanPopup>
    <!--    我的订单弹窗-->
    <VanPopup teleport="body" v-model:show="showOrderRecord" position="bottom">
      <OrderRecordPopup @close="showOrderRecord = false" :orderRestrainStatus="orderRestrainStatus"></OrderRecordPopup>
    </VanPopup>
    <!--抽奖记录弹窗-->
    <VanPopup teleport="body" v-model:show="showDrawRecord" position="bottom">
      <DrawRecordPopup @close="showDrawRecord = false" ></DrawRecordPopup>
    </VanPopup>
    <!--    活动商品弹窗-->
    <VanPopup teleport="body" v-model:show="showGoods" position="bottom">
      <GoodsPopup :data="orderSkuListPreview" @close="showGoods = false"></GoodsPopup>
    </VanPopup>
    <!-- 中奖弹窗 -->
    <VanPopup teleport="body" v-model:show="showAward">
      <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :addressId="addressId" :activityPrizeId="''" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, inject, reactive } from 'vue';
import { showToast } from 'vant';
import furnishStyles, { furnish, prizeInfo } from '../ts/furnishStyles';
import Swiper, { Autoplay } from 'swiper';
import html2canvas from 'html2canvas';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import OrderRecordPopup from '../components/OrderRecordPopup.vue';
import DrawRecordPopup from '../components/DrawRecordPopup.vue';
import { prizeListAll } from '../ts/type';

Swiper.use([Autoplay]);

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
console.log(activityData);
const total = ref(0);

const shopName = ref('xxx自营旗舰店');
const shopLogo = ref('http://img30.360buyimg.com/popshop/jfs/t12040/167/**********/88856/87939c85/5a1e9842N3d659b8f.jpg');

const isLoadingFinish = ref(false);
const showDrawRecord = ref(false);
const showRule = ref(false);
const ruleTest = ref('');

const showMyPrize = ref(false);
const showGoods = ref(false);
const showOrderRecord = ref(false);

// 订单状态
const orderRestrainStatus = ref(0);

type Sku = {
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const isExposure = ref(1);
const skuListPreview = ref<Sku[]>([]);
const orderSkuListPreview = ref<Sku[]>([]);

// 中奖相关信息
const showAward = ref(false);
const award = ref({
  prizeType: 0,
  prizeName: '',
  showImg: '',
  result: {
    result: {
      planDesc: '',
    },
  },
  activityPrizeId: '',
  userPrizeId: '',
});

const activityGiftRecords = reactive([
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '5京豆',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
  {
    userImg: null,
    nickName: '用户***',
    giftName: '20积分',
  },
]);

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
// 是否有选中的盲盒
const isSelectBlindBox = ref(false);
const isSelectBlindBoxIndex = ref(-1);
const toSaveAddress = (id: string) => {
  addressId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 选择盲盒
const selectMhClick = (item:any, index:number) => {
  for (let i = 0; i < prizeListAll.length; i++) {
    prizeListAll[i].isSelect = false;
  }
  prizeListAll[index].isSelect = true;
  isSelectBlindBox.value = true;
  isSelectBlindBoxIndex.value = index + 1;
};
// 换一批
const changeMhDataClick = () => {
  prizeListAll.sort(() => Math.random() - 0.5);
  for (let i = 0; i < prizeListAll.length; i++) {
    prizeListAll[i].position = i + 1;
    prizeListAll[i].isSelect = false;
    prizeListAll[i].isDraw = false;
  }
  isSelectBlindBoxIndex.value = -1;
  isSelectBlindBox.value = false;
};
// 拆盲盒
const startPlay = () => {
  if (!isSelectBlindBox.value) {
    showToast('请先选中心仪盲盒');
    return;
  }
  for (let i = 0; i < prizeListAll.length; i++) {
    if (prizeListAll[i].position === isSelectBlindBoxIndex.value) {
      if (prizeListAll[i].isDraw) {
        showToast('该盲盒已经开过了哦');
        return;
      }
    }
  }
  // 假设后端返回的中奖索引是0
  award.value = {
    prizeType: 0,
    prizeName: '谢谢参与',
    showImg: '',
    result: {
      result: {
        planDesc: '',
      },
    },
    activityPrizeId: '',
    userPrizeId: '',
  };
  showAward.value = true;

  for (let i = 0; i < prizeListAll.length; i++) {
    if (prizeListAll[i].position === isSelectBlindBoxIndex.value) {
      prizeListAll[i].isDraw = true;
    }
  }

};

const ShowToast = () => {
  showToast('活动预览，仅供查看');
};

const myLucky = ref();
const startCallback = async () => {
  // 调用抽奖组件的play方法开始游戏
  myLucky.value.play();
  // 模拟调用接口异步抽奖
  setTimeout(() => {
    // 假设后端返回的中奖索引是0
    const index = Math.floor(Math.random() * 8);
    award.value = {
      prizeType: 0,
      prizeName: '谢谢参与',
      showImg: '',
      result: {
        result: {
          planDesc: '',
        },
      },
      activityPrizeId: '',
      userPrizeId: '',
    };

    // 调用stop停止旋转并传递中奖索引
    myLucky.value.stop(index);
  }, 2000);
};

const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showAward.value = false;
  showSaveAddress.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    showSelect.value = true;
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  console.log(data, type, 'data=============');
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    isLoadingFinish.value = true;
  } else if (type === 'activity') {
    // drawPoints.value = data.points;
    prizeInfo.splice(0);
    if (data.prizeList.length > 0) {
      for (let a = 0; a < data.prizeList.length; a++) {
        if (data.prizeList[a].prizeImg) {
          prizeInfo.push(data.prizeList[a]);
        }
      }
    }
    ruleTest.value = data.rules;
    shopName.value = data.shopName;
    shopLogo.value = data.logoUrl;
    isExposure.value = data.isExposure;
    skuListPreview.value = data.skuListPreview ?? [];
    orderSkuListPreview.value = data.orderSkuListPreview ?? [];
    orderRestrainStatus.value = data.orderRestrainStatus;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'border') {
    showSelect.value = data;
  } else if (type === 'shop') {
    shopName.value = data;
  } else if (type === 'shopLogo') {
    shopLogo.value = data;
  }
};

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    prizeInfo.splice(0);
    for (let a = 0; a < activityData.prizeList.length; a++) {
      if (activityData.prizeList[a].prizeImg) {
        prizeInfo.push(activityData.prizeList[a]);
      }
    }
    ruleTest.value = activityData.rules;
    shopName.value = activityData.shopName;
    shopLogo.value = activityData.logoUrl;
    skuListPreview.value.splice(0);
    skuListPreview.value.push(...activityData.skuListPreview);
    orderSkuListPreview.value.splice(0);
    orderSkuListPreview.value.push(...activityData.orderSkuListPreview);
  }
  if (decoData) {
    console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  if (activityGiftRecords.length > 4) {
    nextTick(() => {
      const mySwiper = new Swiper('.swiper-container', {
        autoplay: {
          delay: 1000,
          stopOnLastSlide: false,
          disableOnInteraction: false,
        },
        direction: 'vertical',
        loop: true,
        slidesPerView: 5,
        loopedSlides: 8,
        centeredSlides: true,
      });
    });
  }
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage);
});
</script>

<style scoped lang="scss">
.select {
  .select-hover {
    border: 2px dashed transparent;
  }

  .select-hover:hover {
    border: 2px dashed red;
  }

  .on-select {
    border: 2px solid #39f !important;
  }
}

.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.37rem 0rem 0.3rem 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0;
  }
  .header{
    margin-top: 0.6rem;
    margin-right: -0.05rem;
  }
  .header-btn {
    width: 1.08rem;
    padding: 0.04rem 0;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    border-radius: 0.22rem;
    border: 0.01rem solid;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.twoDivClass{
  width: 7.5rem;
  height: 11rem;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  .numText{
    position: absolute;
    color:#ffffff;
    font-size: 0.24rem;
    top:0.2rem;
  }
  .prizeClass{
    width: 5rem;
    height: 5rem;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    margin-top: 2.2rem;
    .prizeItemClass{
      flex: 1;
      margin: 0 auto;
      .selectClass{
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/237000/27/5276/2892/65697800F1cd69330/3d3d413da692bff5.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 1.5rem;
        height: 0.49rem;
      }
      .noSelectClass{
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/224061/3/5097/2766/65697800F1c398617/4b73537eb60825b6.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width:1.5rem;
        height: 0.49rem;
      }
      .graySelectClass{
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/226172/37/5583/2225/65697800Fadc3d27e/d313cf6108781f1a.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 1.5rem;
        height: 0.49rem;
      }
        .boxPrizes {
          width: 1.28rem;
          height: 1.28rem;
          margin: 0 auto;
        }
    }
    .bottomBtnDivAll{
      position: absolute;
      bottom: -1.3rem;
      display: flex;
      width: 100%;
      -webkit-box-pack: center;
      justify-content: center;
      .change-blind-box{
        width: 1.2rem;
        height: 0.75rem;
        left: 0.02rem;
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .open-blind-box{
        width: 2.92rem;
        height: 0.83rem;
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .my-prize{
        width: 1.27rem;
        height: 0.75rem;
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }

}

.sku {
  width: 7.5rem;
  margin: 0 auto;
  padding: 0.2rem;
  .sku-list-box {
    background-size: 100%;
    background-repeat: no-repeat;
    width: 6.86rem;
    max-height: 10.3rem;
    margin: 0 auto;
    .skuTitle {
      color:#fff;
      margin:0 auto;
      text-align:center;
    }
    .more-btn-all {
      width:6.9rem;
      .more-btn {
        width: 1.8rem;
        height: 0.5rem;
        font-size: 0.2rem;
        color: #fff;
        background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
        background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
        border-radius: 0.25rem;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto 0.3rem;
      }
    }
    .sku-list {
      width: 6.6rem;
      max-height: 10.2rem;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      display: flex;
      margin: 0.2rem auto 0.1rem auto;
      overflow: hidden;
      overflow-y: scroll;
      .sku-item {
        width: 3.22rem;
        margin-bottom: 0.1rem;
        background: rgb(255, 255, 255);
        border-radius: 0.2rem;
        overflow: hidden;
        img {
          display: block;
          width: 3.22rem;
          height: 3.22rem;
        }
        .sku-text {
          width: 3.4rem;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          font-size: 0.3rem;
          color: #262626;
          line-height: 0.4rem;
          height: 0.8rem;
          padding: 0 0.2rem;
          margin: 0.2rem 0 0.2rem;
          box-sizing: border-box;
        }
        .sku-price {
          font-size: 0.3rem;
          color: #ff5f00;
          padding: 0 0.2rem;
          margin-bottom: 0.2rem;
        }
      }
    }
  }
}

.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 5.96rem;
  margin: 0.49rem auto 0;
  padding-top: 1.1rem;

  .winners-content {
    width: 6.6rem;
    height: 4.7rem;
    background-color: #fff;
    border-radius: 0.1rem;
    margin: 0 auto;
  }
}

.bottom-div {
  padding-top: 0.5rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #fff;
  text-align: center;
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0;
}

.winner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3rem;
  background-color: #fff;
  margin-bottom: 0.05rem;
  border-radius: 0.04rem;
  font-size: 0.26rem;
  img {
    width: 0.6rem;
    height: 0.6rem;
    object-fit: cover;
    border-radius: 1.2rem;
    display: inline;
    vertical-align: middle;
    margin-right: 0.1rem;
  }

  span {
    vertical-align: middle;
    font-size: 0.28rem;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.winner-null {
  text-align: center;
  line-height: 3.9rem;
  font-size: 0.24rem;
  color: #8c8c8c;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none !important;
}
</style>
