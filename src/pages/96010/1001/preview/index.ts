import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decoData = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/327865/29/17695/37427/68be4338F7b8a4b43/81a293b4b299570e.png',
  actBgColor: '',
  shopNameColor: '#000',
  btnColor: '#71361e',
  btnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/330857/19/10769/1236/68be4946Ff256e322/70840a11e3539d2b.png',
  blindBoxBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/340247/31/8290/186245/68be4339F84d002f2/b144a4b34999e89c.png',
  drawBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/347288/10/927/5177/68be433aFc4a7465d/ca4a687c6d4bbb8b.png',
  grayDrawBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/329323/27/10568/4447/68be4337Fca22996c/8e1a557f794da7c9.png',
  blindBox1: 'https://img10.360buyimg.com/imgzone/jfs/t1/329774/17/10786/41714/68be4339F11318d5b/93b5fef870612df1.png',
  blindBox1Light: 'https://img10.360buyimg.com/imgzone/jfs/t1/327446/14/17492/43266/68be4890F934b3767/6bbcdd443a0c6d8e.png',
  blindGif: 'https://img10.360buyimg.com/imgzone/jfs/t1/342406/22/845/2654752/004f7301Fcf5c487a/43972406920078a0.gif',
  winnersDanMu: 'https://img10.360buyimg.com/imgzone/jfs/t1/338742/11/8303/12854/68be433aF7415f89e/6e0fee4b86473cb5.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/337906/26/7412/45639/68be4b18F5f1fedd6/40b3dadd501a5d24.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/338290/26/8243/35073/68be4b19Fea58d602/de132d78e6ee87bc.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/349087/10/731/32324/68be4b18F6c87b66e/2ba3d32f35f30635.png',
  prizeRemainNum: '#feecc4'
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '伊利抽盲盒';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  // app.provide('decoData', decoData);
  app.provide('decoData', _decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
