import { reactive } from 'vue';
import { furnish } from './furnishStyles';
export interface Task {
  id: number;
  taskType: number;
  optWay: number;
  buyType: number;
  perOperateCount: number;
  perLotteryCount: number;
  lotteryCount: number;
  taskFinishCount: number;
  limit: number;
}
export interface FormType {
  realName: string;
  mobile: string;
  province: string;
  city: string;
  county: string;
  address: string;
}
export interface CardType {
  cardDesc: string;
  cardNumber: string;
  cardPassword: string;
  id: number;
  prizeName: string;
  showImg: string;
}

export const prizeListAll = reactive([
  {
    isDraw: false,
    isSelect: false,
    position: 1,
    prizeName: '谢谢参与',
    isDrawLogoImg: '',
    logoImg: furnish.blindBox1,
    lightLogoImg: furnish.blindBox1Light,
  },
  {
    isDraw: false,
    isSelect: false,
    position: 2,
    prizeName: '谢谢参与',
    isDrawLogoImg: '',
    logoImg: furnish.blindBox1,
    lightLogoImg: furnish.blindBox1Light,
  },
  {
    isDraw: false,
    isSelect: false,
    position: 3,
    prizeName: '谢谢参与',
    isDrawLogoImg: '',
    logoImg: furnish.blindBox1,
    lightLogoImg: furnish.blindBox1Light,
  },
  {
    isDraw: false,
    isSelect: false,
    position: 4,
    prizeName: '谢谢参与',
    isDrawLogoImg: '',
    logoImg: furnish.blindBox1,
    lightLogoImg: furnish.blindBox1Light,
  },
  {
    isDraw: false,
    isSelect: false,
    position: 5,
    prizeName: '谢谢参与',
    isDrawLogoImg: '',
    logoImg: furnish.blindBox1,
    lightLogoImg: furnish.blindBox1Light,
  },
  {
    isDraw: false,
    isSelect: false,
    position: 6,
    prizeName: '谢谢参与',
    isDrawLogoImg: '',
    logoImg: furnish.blindBox1,
    lightLogoImg: furnish.blindBox1Light,
  },
  {
    isDraw: false,
    isSelect: false,
    position: 7,
    prizeName: '谢谢参与',
    isDrawLogoImg: '',
    logoImg: furnish.blindBox1,
    lightLogoImg: furnish.blindBox1Light,
  },
  {
    isDraw: false,
    isSelect: false,
    position: 8,
    prizeName: '谢谢参与',
    isDrawLogoImg: '',
    logoImg: furnish.blindBox1,
    lightLogoImg: furnish.blindBox1Light,
  },
  {
    isDraw: false,
    isSelect: false,
    position: 9,
    prizeName: '谢谢参与',
    isDrawLogoImg: '',
    logoImg: furnish.blindBox1,
    lightLogoImg: furnish.blindBox1Light,
  },
]);
