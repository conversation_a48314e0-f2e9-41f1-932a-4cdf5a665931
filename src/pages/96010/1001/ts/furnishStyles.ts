import { computed, reactive } from 'vue';

export const prizeInfo = reactive([
  {
    id: '',
    index: 1,
    prizeImg: '',
    prizeName: '谢谢参与',
    prizeType: 0,
  },
]);

export const furnish = reactive(
{
  actBg: "",
  pageBg: "",
  actBgColor: "",
  shopNameColor: "",
  btnColor: "",
  btnBg: "",
  blindBoxBg: "",
  drawBtn: "",
  grayDrawBtn: "",
  blindBox1: "",
  blindBox1Light: "",
  blindGif: "",
  winnersDanMu: "",
  mpImg: "",
  cmdImg: "",
  h5Img: "",
  prizeRemainNum: "",
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
  backgroundImage: furnish.btnBg ? `url("${furnish.btnBg}")` : '',
}));

const drawBtn = computed(() => ({
  backgroundImage: furnish.drawBtn ? `url(${furnish.drawBtn})` : '',
}));

const blindBoxBg = computed(() => ({
  backgroundImage: furnish.blindBoxBg ? `url("${furnish.blindBoxBg}")` : '',
}));

const grayDrawBtn = computed(() => ({
  backgroundImage: furnish.grayDrawBtn ? `url("${furnish.grayDrawBtn}")` : '',
}));

const blindBox1 = computed(() => ({
  backgroundImage: furnish.blindBox1 ? `url("${furnish.blindBox1}")` : 'https://img10.360buyimg.com/imgzone/jfs/t1/329774/17/10786/41714/68be4339F11318d5b/93b5fef870612df1.png',
}));

const blindBox1Light = computed(() => ({
  backgroundImage: furnish.blindBox1Light ? `url("${furnish.blindBox1Light}")` : '',
}));

const prizeRemainNum = computed(() => ({
  color: furnish.prizeRemainNum ?? '',
}));


const wheelTpl = {
  hasImg: {
    fonts: [{ text: '', top: '30%', fontColor: 'rgb(131, 75, 235)', fontSize: '0.2rem' }],
    imgs: [
      {
        src: '',
        width: '35%',
        top: '55%',
        borderRadius: '50%',
      },
    ],
  },
  noImg: { fonts: [{ text: '', top: '30%', fontColor: 'rgb(131, 75, 235)', fontSize: '0.2rem' }] },
};

export default {
  pageBg,
  shopNameColor,
  headerBtn,
  blindBoxBg,
  grayDrawBtn,
  blindBox1,
  blindBox1Light,
  drawBtn,
  prizeRemainNum,
};
