import { computed, reactive } from 'vue';
export const furnish = reactive<{ [x: string]: string }>({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  ruleBtn: '', // 活动按钮
  btnTextColor: '',
  step1Bg: '',
  getDemoPrizeBtn: '',
  step2Bg: '',
  step2ItemBg: '',
  step3Bg: '',
  skuBg: '',
  blindBox1: '', // 盲盒图片1
  blindBox1Light: '', // 盲盒图片1高亮版
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
}));

const ruleBtn = computed(() => ({
  backgroundImage: furnish.ruleBtn ? `url("${furnish.ruleBtn}")` : '',
  color: furnish.btnTextColor,
}));

const step1Bg = computed(() => ({
  backgroundImage: furnish.step1Bg ? `url("${furnish.step1Bg}")` : '',
}));

const getDemoPrizeBtn = computed(() => ({
  backgroundImage: furnish.getDemoPrizeBtn ? `url("${furnish.getDemoPrizeBtn}")` : '',
}));

const step2Bg = computed(() => ({
  backgroundImage: furnish.step2Bg ? `url("${furnish.step2Bg}")` : '',
}));

const step2ItemBg = computed(() => ({
  backgroundImage: furnish.step2ItemBg ? `url("${furnish.step2ItemBg}")` : '',
}));

const step3Bg = computed(() => ({
  backgroundImage: furnish.step3Bg ? `url("${furnish.step3Bg}")` : '',
}));

const skuBg = computed(() => ({
  backgroundImage: furnish.skuBg ? `url("${furnish.skuBg}")` : '',
}));

export default {
  pageBg,
  ruleBtn,
  step1Bg,
  getDemoPrizeBtn,
  step2Bg,
  step2ItemBg,
  step3Bg,
  skuBg,
};
